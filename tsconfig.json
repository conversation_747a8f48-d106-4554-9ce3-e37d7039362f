{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "noImplicitAny": false, "noUnusedParameters": false, "skipLibCheck": true, "allowJs": true, "noUnusedLocals": false, "strictNullChecks": false, "target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "strict": false, "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}