import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Calendar, DollarSign } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import speakerImage from "@/assets/speaker.jpg";
import tripodImage from "@/assets/tripod.jpg";
import stabilizerImage from "@/assets/stabilizer.jpg";

const FeaturedEquipment = () => {
  const equipment = [
    {
      id: 1,
      name: "Professional Wireless Speaker System",
      category: "Audio",
      image: speakerImage,
      dailyRate: 45,
      weeklyRate: 250,
      rating: 4.9,
      reviews: 127,
      available: true,
      features: ["Bluetooth 5.0", "12-hour battery", "Weather resistant"]
    },
    {
      id: 2,
      name: "Carbon Fiber Camera Tripod",
      category: "Camera Equipment",
      image: tripodImage,
      dailyRate: 25,
      weeklyRate: 140,
      rating: 4.8,
      reviews: 89,
      available: true,
      features: ["Max height 65\"", "Supports 22lbs", "Lightweight"]
    },
    {
      id: 3,
      name: "3-Axis Camera Stabilizer",
      category: "Camera Equipment", 
      image: stabilizerImage,
      dailyRate: 65,
      weeklyRate: 350,
      rating: 4.9,
      reviews: 156,
      available: true,
      features: ["3-axis gimbal", "12-hour runtime", "Smartphone app"]
    }
  ];

  return (
    <section className="py-16 bg-section-gradient">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Featured Equipment
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover our most popular rental items. Professional-grade equipment 
            trusted by event organizers and photographers worldwide.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {equipment.map((item, index) => (
            <Card 
              key={item.id} 
              className="group hover:shadow-card-hover transition-all duration-300 hover:-translate-y-1 animate-fade-in border-0 shadow-card"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <CardHeader className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img 
                    src={item.image} 
                    alt={item.name}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-accent text-accent-foreground">
                      {item.category}
                    </Badge>
                  </div>
                  {item.available && (
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-green-500 text-white">
                        Available
                      </Badge>
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="p-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                      {item.name}
                    </h3>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      <Star className="w-4 h-4 fill-accent text-accent" />
                      <span>{item.rating}</span>
                      <span>({item.reviews} reviews)</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    {item.features.map((feature, idx) => (
                      <div key={idx} className="text-sm text-muted-foreground">
                        • {feature}
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="w-4 h-4 text-muted-foreground" />
                        <span className="text-lg font-semibold">${item.dailyRate}/day</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        ${item.weeklyRate}/week
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>

              <CardFooter className="p-6 pt-0">
                <div className="flex gap-2 w-full">
                  <Button 
                    asChild 
                    className="flex-1 bg-hero-gradient hover:opacity-90"
                  >
                    <Link to={`/equipment/${item.id}`}>
                      View Details
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" className="px-4">
                    <Calendar className="w-4 h-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <Button 
            asChild 
            variant="outline" 
            size="lg"
            className="px-8 hover:border-primary hover:text-primary"
          >
            <Link to="/equipment">
              View All Equipment
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedEquipment;