import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, CheckCircle } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import heroImage from "@/assets/hero-equipment.jpg";

const HeroSection = () => {
  const features = [
    "Located in Melbourne CBD",
    "Professional grade equipment",
    "Competitive daily & weekly rates",
    "Free delivery within Melbourne metro"
  ];

  return (
    <section className="relative min-h-[90vh] flex items-center overflow-hidden">
      {/* Background with overlay */}
      <div className="absolute inset-0">
        <img 
          src={heroImage} 
          alt="Professional equipment rental" 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-background/95 via-background/80 to-transparent"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-2xl">
          <div className="space-y-6 animate-fade-in">
            <div className="space-y-4">
              <h1 className="text-4xl md:text-6xl font-bold leading-tight">
                Event Equipment
                <span className="bg-text-gradient bg-clip-text text-transparent"> Rental</span>
                <br />
                in Melbourne CBD
              </h1>
              <p className="text-lg md:text-xl text-muted-foreground leading-relaxed">
                Professional audio, camera, and lighting equipment available for rent
                right in the heart of Melbourne CBD. Quality gear, competitive rates, convenient location.
              </p>
            </div>

            {/* Features List */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {features.map((feature, index) => (
                <div 
                  key={index} 
                  className="flex items-center space-x-2 animate-slide-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CheckCircle className="w-5 h-5 text-accent flex-shrink-0" />
                  <span className="text-sm text-muted-foreground">{feature}</span>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Button 
                asChild 
                size="lg" 
                className="bg-hero-gradient hover:opacity-90 text-base px-8 transition-all hover:scale-105"
              >
                <Link to="/equipment">
                  Browse Equipment
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button 
                asChild 
                variant="outline" 
                size="lg"
                className="text-base px-8 hover:border-primary hover:text-primary"
              >
                <Link to="/contact">
                  Get Quote
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-primary/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;