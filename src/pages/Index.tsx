import Header from "@/components/Header";
import Footer from "@/components/Footer";
import HeroSection from "@/components/HeroSection";
import FeaturedEquipment from "@/components/FeaturedEquipment";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Shield, Clock, Headphones, Truck, Star, Users, Calendar, Award } from "lucide-react";
import { Link } from "react-router-dom";

const Index = () => {
  const features = [
    {
      icon: Shield,
      title: "Insured Equipment",
      description: "All our equipment is fully insured and maintained to the highest standards."
    },
    {
      icon: Clock,
      title: "24/7 Support",
      description: "Round-the-clock technical support to ensure your event runs smoothly."
    },
    {
      icon: Truck,
      title: "Melbourne Metro Delivery",
      description: "Free delivery and pickup throughout Melbourne metro area for convenient access."
    },
    {
      icon: Headphones,
      title: "Expert Guidance",
      description: "Our team helps you choose the right equipment for your specific needs."
    }
  ];

  const stats = [
    { icon: Users, value: "5,000+", label: "Happy Customers" },
    { icon: Calendar, value: "10,000+", label: "Events Supported" },
    { icon: Award, value: "99.8%", label: "Reliability Rate" },
    { icon: Star, value: "4.9", label: "Average Rating" }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main>
        <HeroSection />
        
        {/* Features Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Why Choose EquipRent?
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Located in Melbourne CBD, we provide convenient access to professional
                equipment with exceptional service for your events.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <Card 
                  key={index} 
                  className="text-center border-0 shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-1 animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CardContent className="p-6 space-y-4">
                    <div className="w-12 h-12 bg-hero-gradient rounded-lg flex items-center justify-center mx-auto">
                      <feature.icon className="w-6 h-6 text-primary-foreground" />
                    </div>
                    <h3 className="text-xl font-semibold">{feature.title}</h3>
                    <p className="text-muted-foreground text-sm leading-relaxed">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        <FeaturedEquipment />

        {/* Stats Section */}
        <section className="py-16 bg-hero-gradient text-primary-foreground">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              {stats.map((stat, index) => (
                <div 
                  key={index} 
                  className="space-y-2 animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <stat.icon className="w-8 h-8 mx-auto mb-4 opacity-80" />
                  <div className="text-3xl md:text-4xl font-bold">{stat.value}</div>
                  <div className="text-sm opacity-90">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-3xl mx-auto space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold">
                Ready to Elevate Your Event?
              </h2>
              <p className="text-lg text-muted-foreground">
                Browse our equipment catalog or visit our Melbourne CBD location for a custom quote.
                Our team is ready to help make your event unforgettable.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  asChild 
                  size="lg" 
                  className="bg-hero-gradient hover:opacity-90 px-8"
                >
                  <Link to="/equipment">
                    Browse Equipment
                  </Link>
                </Button>
                <Button 
                  asChild 
                  variant="outline" 
                  size="lg"
                  className="px-8 hover:border-primary hover:text-primary"
                >
                  <Link to="/contact">
                    Get Custom Quote
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Index;
