import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Clock, ArrowRight, User } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const Blog = () => {
  const blogPosts = [
    {
      id: 1,
      title: "10 Essential Tips for Event Photography Success",
      excerpt: "Master the art of event photography with these professional tips that will help you capture memorable moments and deliver stunning results.",
      category: "Photography",
      author: "<PERSON>",
      date: "2024-01-15",
      readTime: "5 min read",
      image: "https://images.unsplash.com/photo-1492691527719-9d1e07e534b4?w=800&h=400&fit=crop",
      featured: true
    },
    {
      id: 2,
      title: "Choosing the Right Audio Equipment for Your Event",
      excerpt: "A comprehensive guide to selecting speakers, microphones, and audio accessories that will ensure crystal-clear sound quality.",
      category: "Audio",
      author: "<PERSON>",
      date: "2024-01-10",
      readTime: "7 min read",
      image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=400&fit=crop",
      featured: false
    },
    {
      id: 3,
      title: "Lighting Techniques for Professional Video Production",
      excerpt: "Learn how to use professional lighting equipment to create cinematic looks and enhance the visual quality of your videos.",
      category: "Lighting",
      author: "Emma Chen",
      date: "2024-01-05",
      readTime: "6 min read",
      image: "https://images.unsplash.com/photo-1516035069371-29a1b244cc32?w=800&h=400&fit=crop",
      featured: false
    },
    {
      id: 4,
      title: "Budget-Friendly Equipment Rental Strategies",
      excerpt: "Maximize your budget with smart rental strategies and timing tips that professional event organizers use.",
      category: "Business",
      author: "David Park",
      date: "2024-01-01",
      readTime: "4 min read",
      image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&h=400&fit=crop",
      featured: false
    },
    {
      id: 5,
      title: "Camera Stabilizer Setup and Best Practices",
      excerpt: "Get the most out of your gimbal stabilizer with proper setup techniques and professional shooting methods.",
      category: "Equipment",
      author: "Lisa Thompson",
      date: "2023-12-28",
      readTime: "8 min read",
      image: "https://images.unsplash.com/photo-1521737711867-e3b97375f902?w=800&h=400&fit=crop",
      featured: false
    },
    {
      id: 6,
      title: "Outdoor Event Challenges and Solutions",
      excerpt: "Navigate weather conditions, power requirements, and logistics when planning outdoor events with rental equipment.",
      category: "Events",
      author: "Alex Rivera",
      date: "2023-12-25",
      readTime: "6 min read",
      image: "https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?w=800&h=400&fit=crop",
      featured: false
    }
  ];

  const categories = ["All", "Photography", "Audio", "Lighting", "Business", "Equipment", "Events"];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="py-16 bg-section-gradient">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Tips & Insights
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Learn from professionals with our comprehensive guides on equipment usage, 
              event planning, and industry best practices.
            </p>
          </div>
        </section>

        {/* Categories */}
        <section className="py-8 border-b border-border">
          <div className="container mx-auto px-4">
            <div className="flex flex-wrap gap-2 justify-center">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant="outline"
                  className="hover:border-primary hover:text-primary"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Post */}
        {blogPosts.find(post => post.featured) && (
          <section className="py-12">
            <div className="container mx-auto px-4">
              <h2 className="text-2xl font-bold mb-8">Featured Article</h2>
              {(() => {
                const featured = blogPosts.find(post => post.featured)!;
                return (
                  <Card className="overflow-hidden shadow-card-hover border-0">
                    <div className="md:flex">
                      <div className="md:w-1/2">
                        <img 
                          src={featured.image} 
                          alt={featured.title}
                          className="w-full h-64 md:h-full object-cover"
                        />
                      </div>
                      <div className="md:w-1/2 p-8">
                        <div className="space-y-4">
                          <Badge className="bg-accent text-accent-foreground">
                            {featured.category}
                          </Badge>
                          <h3 className="text-2xl md:text-3xl font-bold leading-tight">
                            {featured.title}
                          </h3>
                          <p className="text-muted-foreground text-lg leading-relaxed">
                            {featured.excerpt}
                          </p>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <User className="w-4 h-4" />
                              <span>{featured.author}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Calendar className="w-4 h-4" />
                              <span>{new Date(featured.date).toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="w-4 h-4" />
                              <span>{featured.readTime}</span>
                            </div>
                          </div>
                          <Button className="bg-hero-gradient hover:opacity-90">
                            Read Article
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                );
              })()}
            </div>
          </section>
        )}

        {/* Blog Grid */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold mb-8">Latest Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {blogPosts.filter(post => !post.featured).map((post, index) => (
                <Card 
                  key={post.id} 
                  className="group hover:shadow-card-hover transition-all duration-300 hover:-translate-y-1 border-0 shadow-card animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CardHeader className="p-0">
                    <div className="relative overflow-hidden rounded-t-lg">
                      <img 
                        src={post.image} 
                        alt={post.title}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-accent text-accent-foreground">
                          {post.category}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <h3 className="text-xl font-semibold leading-tight group-hover:text-primary transition-colors">
                        {post.title}
                      </h3>
                      <p className="text-muted-foreground text-sm leading-relaxed">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{post.author}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>{new Date(post.date).toLocaleDateString()}</span>
                          </div>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                      <Button 
                        variant="outline" 
                        className="w-full hover:border-primary hover:text-primary"
                      >
                        Read More
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Blog;