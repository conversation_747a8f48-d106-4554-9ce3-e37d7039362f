import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Star, Calendar, DollarSign, Search, Filter } from "lucide-react";
import { Link } from "react-router-dom";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import speakerImage from "@/assets/speaker.jpg";
import tripodImage from "@/assets/tripod.jpg";
import stabilizerImage from "@/assets/stabilizer.jpg";

const Equipment = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [sortBy, setSortBy] = useState("name");

  const equipment = [
    {
      id: 1,
      name: "Professional Wireless Speaker System",
      category: "Audio",
      image: speakerImage,
      dailyRate: 45,
      weeklyRate: 250,
      rating: 4.9,
      reviews: 127,
      available: true,
      description: "High-quality wireless speaker with crystal clear sound and powerful bass.",
      features: ["Bluetooth 5.0", "12-hour battery", "Weather resistant", "600W output"]
    },
    {
      id: 2,
      name: "Carbon Fiber Camera Tripod",
      category: "Camera Equipment",
      image: tripodImage,
      dailyRate: 25,
      weeklyRate: 140,
      rating: 4.8,
      reviews: 89,
      available: true,
      description: "Lightweight yet sturdy carbon fiber tripod perfect for professional photography.",
      features: ["Max height 65\"", "Supports 22lbs", "Lightweight design", "Quick-release plate"]
    },
    {
      id: 3,
      name: "3-Axis Camera Stabilizer",
      category: "Camera Equipment", 
      image: stabilizerImage,
      dailyRate: 65,
      weeklyRate: 350,
      rating: 4.9,
      reviews: 156,
      available: true,
      description: "Advanced 3-axis gimbal stabilizer for smooth, professional video recording.",
      features: ["3-axis gimbal", "12-hour runtime", "Smartphone app", "Follow focus"]
    },
    {
      id: 4,
      name: "Professional Projector",
      category: "Visual",
      image: speakerImage, // Placeholder
      dailyRate: 0,
      weeklyRate: 0,
      rating: 0,
      reviews: 0,
      available: false,
      description: "High-brightness projector for large venues and outdoor events.",
      features: ["4K resolution", "5000 lumens", "Wireless connectivity"]
    },
    {
      id: 5,
      name: "Projection Screen",
      category: "Visual",
      image: tripodImage, // Placeholder
      dailyRate: 0,
      weeklyRate: 0,
      rating: 0,
      reviews: 0,
      available: false,
      description: "Portable projection screen for professional presentations.",
      features: ["120\" diagonal", "Portable stand", "Anti-wrinkle surface"]
    }
  ];

  const categories = ["all", "Audio", "Camera Equipment", "Visual", "Lighting"];

  const filteredEquipment = equipment
    .filter(item => 
      searchTerm === "" || 
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(item => categoryFilter === "all" || item.category === categoryFilter)
    .sort((a, b) => {
      switch (sortBy) {
        case "price-low":
          return a.dailyRate - b.dailyRate;
        case "price-high":
          return b.dailyRate - a.dailyRate;
        case "rating":
          return b.rating - a.rating;
        default:
          return a.name.localeCompare(b.name);
      }
    });

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="py-16 bg-section-gradient">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Equipment Catalog
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Browse our comprehensive collection of professional equipment for rent. 
              From audio gear to camera equipment, we have everything you need.
            </p>
          </div>
        </section>

        {/* Filters */}
        <section className="py-8 border-b border-border">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row gap-4 items-center">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search equipment..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-4">
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-48">
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category === "all" ? "All Categories" : category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name (A-Z)</SelectItem>
                    <SelectItem value="price-low">Price (Low to High)</SelectItem>
                    <SelectItem value="price-high">Price (High to Low)</SelectItem>
                    <SelectItem value="rating">Highest Rated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </section>

        {/* Equipment Grid */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            {filteredEquipment.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-lg text-muted-foreground">No equipment found matching your criteria.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredEquipment.map((item) => (
                  <Card 
                    key={item.id} 
                    className={`group hover:shadow-card-hover transition-all duration-300 hover:-translate-y-1 border-0 shadow-card ${
                      !item.available ? 'opacity-75' : ''
                    }`}
                  >
                    <CardHeader className="p-0">
                      <div className="relative overflow-hidden rounded-t-lg">
                        <img 
                          src={item.image} 
                          alt={item.name}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                        <div className="absolute top-4 left-4">
                          <Badge className="bg-accent text-accent-foreground">
                            {item.category}
                          </Badge>
                        </div>
                        <div className="absolute top-4 right-4">
                          <Badge className={item.available ? "bg-green-500 text-white" : "bg-red-500 text-white"}>
                            {item.available ? "Available" : "Coming Soon"}
                          </Badge>
                        </div>
                        {!item.available && (
                          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                            <span className="text-white font-semibold">Coming Soon</span>
                          </div>
                        )}
                      </div>
                    </CardHeader>

                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div>
                          <h3 className="font-semibold mb-1 group-hover:text-primary transition-colors">
                            {item.name}
                          </h3>
                          {item.available && (
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <Star className="w-4 h-4 fill-accent text-accent" />
                              <span>{item.rating}</span>
                              <span>({item.reviews} reviews)</span>
                            </div>
                          )}
                        </div>

                        {item.available && (
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center space-x-2">
                                <DollarSign className="w-4 h-4 text-muted-foreground" />
                                <span className="font-semibold">${item.dailyRate}/day</span>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                ${item.weeklyRate}/week
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>

                    <CardFooter className="p-4 pt-0">
                      {item.available ? (
                        <div className="flex gap-2 w-full">
                          <Button 
                            asChild 
                            className="flex-1 bg-hero-gradient hover:opacity-90"
                          >
                            <Link to={`/equipment/${item.id}`}>
                              View Details
                            </Link>
                          </Button>
                          <Button variant="outline" size="sm" className="px-4">
                            <Calendar className="w-4 h-4" />
                          </Button>
                        </div>
                      ) : (
                        <Button disabled className="w-full">
                          Notify When Available
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Equipment;